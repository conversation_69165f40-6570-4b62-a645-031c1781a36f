#welcome {
  text-align: center;
  background-image: radial-gradient(
      circle at center,
      var(--bg-color),
      transparent 16em
    ), radial-gradient(circle at center, var(--bg-color), transparent 8em);
  background-repeat: no-repeat;
  background-attachment: fixed;
  cursor: default;

  @media (prefers-color-scheme: dark) {
    background-image: none;
  }

  .hero-container {
    padding: 16px;
    height: 100vh;
    height: 100svh;
    max-height: 800px;
    display: flex;
    flex-direction: column;
  }

  .hero-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  h1 {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0;
    padding: 0;
    font-size: 5em;
    line-height: 1;
    letter-spacing: -1px;
    position: relative;

    img {
      filter: drop-shadow(-1px -1px var(--bg-blur-color))
        drop-shadow(0 -1px 1px #fff)
        drop-shadow(0 16px 32px var(--drop-shadow-color));

      @media (prefers-color-scheme: dark) {
        filter: none;
      }
    }

    &:hover img {
      transform: scale(1.05);
    }
  }

  img {
    vertical-align: top;
    transition: transform 0.3s ease-out;
  }

  .desc {
    font-size: 1.4em;
    text-wrap: balance;
    opacity: 0.7;

    & ~ p {
      margin-top: 0;
    }
  }

  .hero-container > p {
    margin-top: 0;
  }

  .app-site-version {
    text-align: center;
    opacity: 0.5;
    color: var(--text-insignificant-color);
    font-family: var(--monospace-font), monospace;

    small {
      font-size: 11px;
      letter-spacing: -0.2px;
    }
  }

  #why-container {
    padding: 0 16px;
  }

  .sections {
    padding-inline: 16px;

    section {
      text-align: start;
      max-width: 480px;
      background-color: var(--bg-color);
      border-radius: 16px;
      overflow: hidden;
      box-shadow: 17px 20px 40px var(--drop-shadow-color);
      margin-bottom: 48px;

      h4 {
        margin: 0;
        padding: 30px 30px 0;
        font-size: 1.4em;
        font-weight: 600;
      }

      p {
        margin-inline: 30px;
        margin-bottom: 30px;
        opacity: 0.7;
        text-wrap: balance;
      }

      img {
        width: 100%;
        height: auto;
        border-bottom: 1px solid var(--outline-color);

        @media (prefers-color-scheme: dark) {
          filter: invert(0.85) hue-rotate(180deg);
        }
      }
    }
  }

  @media (width > 40em) {
    /* display: grid;
    grid-template-columns: 1fr 1fr;
    height: 100vh;
    height: 100svh; */
    width: 100%;

    .hero-container {
      height: auto;
      max-height: none;
      position: fixed;
      inset-inline-start: 0;
      top: 0;
      bottom: 0;
      width: 50%;
      align-items: flex-end;

      > * {
        max-width: 40em;
        width: 100%;
      }
    }

    #why-container {
      padding: 32px;
      padding-inline-start: 8px;
      margin-inline-start: 50%;

      /* overflow: auto;
      mask-image: linear-gradient(to top, transparent 16px, black 64px); */
    }
  }

  & ~ :is(#compose-button, #shortcuts) {
    display: none;
  }
}
